import bcrypt from 'bcryptjs';
import { TestDatabase, createMockSession } from '../utils/testHelpers';
import { testUsers, testSecurityInputs } from '../fixtures/testData';

describe('Authentication Unit Tests', () => {
  let testDb: TestDatabase;

  beforeAll(async () => {
    testDb = new TestDatabase();
  });

  beforeEach(async () => {
    await testDb.cleanup();
  });

  afterAll(async () => {
    await testDb.cleanup();
    await testDb.disconnect();
  });

  describe('Password Hashing', () => {
    it('should hash passwords securely', async () => {
      const password = 'TestPassword123!';
      const hashedPassword = await bcrypt.hash(password, 10);
      
      expect(hashedPassword).not.toBe(password);
      expect(hashedPassword.length).toBeGreaterThan(50);
      expect(await bcrypt.compare(password, hashedPassword)).toBe(true);
    });

    it('should generate different hashes for same password', async () => {
      const password = 'TestPassword123!';
      const hash1 = await bcrypt.hash(password, 10);
      const hash2 = await bcrypt.hash(password, 10);
      
      expect(hash1).not.toBe(hash2);
      expect(await bcrypt.compare(password, hash1)).toBe(true);
      expect(await bcrypt.compare(password, hash2)).toBe(true);
    });

    it('should reject incorrect passwords', async () => {
      const password = 'TestPassword123!';
      const wrongPassword = 'WrongPassword123!';
      const hashedPassword = await bcrypt.hash(password, 10);
      
      expect(await bcrypt.compare(wrongPassword, hashedPassword)).toBe(false);
    });
  });

  describe('User Creation', () => {
    it('should create user with valid data', async () => {
      const userData = testUsers.validUser;
      const user = await testDb.createTestUser(userData);
      
      expect(user).toHaveProperty('id');
      expect(user.email).toBe(userData.email);
      expect(user.name).toBe(userData.name);
      expect(user.password).not.toBe(userData.password); // Should be hashed
    });

    it('should enforce unique email constraint', async () => {
      const userData = testUsers.validUser;
      await testDb.createUserForUniqueTest(userData);

      await expect(testDb.createUserForUniqueTest(userData)).rejects.toThrow();
    });

    it('should handle missing required fields', async () => {
      const incompleteData = { email: '<EMAIL>' }; // Missing password
      
      await expect(testDb.createTestUser(incompleteData)).rejects.toThrow();
    });
  });

  describe('Session Management', () => {
    it('should create valid session object', () => {
      const userId = 'test-user-id';
      const userEmail = '<EMAIL>';
      const session = createMockSession(userId, userEmail);
      
      expect(session.user.id).toBe(userId);
      expect(session.user.email).toBe(userEmail);
      expect(session.expires).toBeDefined();
      expect(new Date(session.expires).getTime()).toBeGreaterThan(Date.now());
    });

    it('should include required session properties', () => {
      const session = createMockSession('test-id');
      
      expect(session).toHaveProperty('user');
      expect(session).toHaveProperty('expires');
      expect(session.user).toHaveProperty('id');
      expect(session.user).toHaveProperty('email');
    });
  });

  describe('Security Validations', () => {
    it('should reject SQL injection attempts in email', async () => {
      for (const maliciousEmail of testSecurityInputs.sqlInjectionAttempts) {
        const userData = {
          ...testUsers.validUser,
          email: maliciousEmail
        };
        
        // Should either reject or sanitize the input
        try {
          const user = await testDb.createTestUser(userData);
          // If creation succeeds, email should be sanitized
          expect(user.email).not.toContain('DROP TABLE');
          expect(user.email).not.toContain('UNION SELECT');
        } catch (error) {
          // Rejection is also acceptable
          expect(error).toBeDefined();
        }
      }
    });

    it('should handle oversized input gracefully', async () => {
      const oversizedData = {
        ...testUsers.validUser,
        email: testSecurityInputs.oversizedInputs.longEmail,
        name: testSecurityInputs.oversizedInputs.longString
      };
      
      await expect(testDb.createTestUser(oversizedData)).rejects.toThrow();
    });

    it('should validate email format', async () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'test@',
        '<EMAIL>',
        'test@example',
        ''
      ];
      
      for (const invalidEmail of invalidEmails) {
        const userData = {
          ...testUsers.validUser,
          email: invalidEmail
        };
        
        await expect(testDb.createTestUser(userData)).rejects.toThrow();
      }
    });
  });

  describe('Password Security', () => {
    it('should enforce minimum password requirements', async () => {
      const weakPasswords = [
        '123',           // Too short
        'password',      // No numbers/special chars
        '12345678',      // Only numbers
        'PASSWORD',      // Only uppercase
        'password123'    // No special characters
      ];
      
      for (const weakPassword of weakPasswords) {
        const hashedPassword = await bcrypt.hash(weakPassword, 10);
        const userData = {
          ...testUsers.validUser,
          password: hashedPassword
        };
        
        // The user creation might succeed (since we're hashing),
        // but the original password validation should happen at the API level
        const user = await testDb.createTestUser(userData);
        expect(user).toBeDefined();
      }
    });

    it('should handle special characters in passwords', async () => {
      const specialCharPassword = 'Test!@#$%^&*()_+{}|:"<>?[]\\;\',./-=`~123';
      const hashedPassword = await bcrypt.hash(specialCharPassword, 10);
      const userData = {
        ...testUsers.validUser,
        password: hashedPassword
      };
      
      const user = await testDb.createTestUser(userData);
      expect(user).toBeDefined();
      expect(await bcrypt.compare(specialCharPassword, user.password)).toBe(true);
    });
  });

  describe('Account Security Features', () => {
    it('should track failed login attempts', async () => {
      const user = await testDb.createTestUser({
        ...testUsers.validUser,
        failedLoginAttempts: 3
      });
      
      expect(user.failedLoginAttempts).toBe(3);
    });

    it('should handle account lockout', async () => {
      const lockoutTime = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes from now
      const user = await testDb.createTestUser({
        ...testUsers.validUser,
        failedLoginAttempts: 5,
        lockedUntil: lockoutTime
      });
      
      expect(user.lockedUntil).toBeDefined();
      expect(user.lockedUntil!.getTime()).toBeGreaterThan(Date.now());
    });

    it('should handle password reset tokens', async () => {
      const resetToken = 'test-reset-token-hash';
      const resetExpiry = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now
      
      const user = await testDb.createTestUser({
        ...testUsers.validUser,
        passwordResetToken: resetToken,
        passwordResetExpires: resetExpiry
      });
      
      expect(user.passwordResetToken).toBe(resetToken);
      expect(user.passwordResetExpires).toBeDefined();
      expect(user.passwordResetExpires!.getTime()).toBeGreaterThan(Date.now());
    });
  });

  describe('User Data Integrity', () => {
    it('should maintain data consistency', async () => {
      const user = await testDb.createTestUser(testUsers.validUser);
      
      expect(user.createdAt).toBeDefined();
      expect(user.updatedAt).toBeDefined();
      expect(user.createdAt.getTime()).toBeLessThanOrEqual(user.updatedAt.getTime());
    });

    it('should handle optional fields correctly', async () => {
      const minimalUser = {
        email: '<EMAIL>',
        password: await bcrypt.hash('TestPassword123!', 10)
      };
      
      const user = await testDb.createTestUser(minimalUser);
      
      expect(user.email).toBe(minimalUser.email);
      expect(user.name).toBeNull();
      expect(user.image).toBeNull();
      expect(user.emailVerified).toBeNull();
    });
  });
});
