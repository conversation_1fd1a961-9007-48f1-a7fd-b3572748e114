/**
 * Optimized Query Service
 * Addresses specific performance bottlenecks identified in the assessment and data access patterns
 */

import prisma from './prisma';
import { consolidatedCache } from './services/consolidated-cache-service';
import { enhancedCacheKeyGenerator } from './enhancedCacheKeyGenerator';
import { performance } from 'perf_hooks';

interface QueryOptimization {
  useCache: boolean;
  cacheTTL: number;
  batchingEnabled: boolean;
  indexHints: string[];
  selectOptimization: boolean;
}

interface BatchedQuery<T> {
  id: string;
  query: () => Promise<T>;
  resolve: (value: T) => void;
  reject: (error: any) => void;
  priority: number;
}

export class OptimizedQueryService {
  private static instance: OptimizedQueryService;
  private batchQueues = new Map<string, BatchedQuery<any>[]>();
  private batchTimers = new Map<string, NodeJS.Timeout>();
  private queryMetrics = new Map<string, { count: number; totalTime: number; avgTime: number }>();

  private readonly BATCH_DELAY = 25; // 25ms for faster batching
  private readonly MAX_BATCH_SIZE = 15; // Increased batch size
  private readonly CACHE_STRATEGIES = {
    assessment: { useCache: true, cacheTTL: 600, batchingEnabled: true, indexHints: ['userId', 'status'], selectOptimization: true },
    careerPaths: { useCache: true, cacheTTL: 1800, batchingEnabled: false, indexHints: ['isActive'], selectOptimization: true },
    learningResources: { useCache: true, cacheTTL: 900, batchingEnabled: true, indexHints: ['isActive', 'category'], selectOptimization: true },
    userProgress: { useCache: true, cacheTTL: 300, batchingEnabled: true, indexHints: ['userId', 'status'], selectOptimization: true },
    forumPosts: { useCache: true, cacheTTL: 180, batchingEnabled: true, indexHints: ['isHidden', 'createdAt'], selectOptimization: true }
  };

  static getInstance(): OptimizedQueryService {
    if (!OptimizedQueryService.instance) {
      OptimizedQueryService.instance = new OptimizedQueryService();
    }
    return OptimizedQueryService.instance;
  }

  /**
   * Optimized assessment queries with intelligent caching
   */
  async getOptimizedUserAssessment(userId: string, includeResponses: boolean = false): Promise<any> {
    const cacheKey = enhancedCacheKeyGenerator.generateKey(
      'assessment',
      'user_assessment',
      { includeResponses },
      userId
    );

    return this.executeOptimizedQuery(
      cacheKey,
      async () => {
        const selectFields = includeResponses ? {
          id: true,
          status: true,
          currentStep: true,
          completedAt: true,
          updatedAt: true,
          responses: {
            select: {
              questionKey: true,
              answerValue: true
            }
          }
        } : {
          id: true,
          status: true,
          currentStep: true,
          completedAt: true,
          updatedAt: true
        };

        return prisma.assessment.findFirst({
          where: { userId, status: { in: ['IN_PROGRESS', 'COMPLETED'] } },
          select: selectFields,
          orderBy: { updatedAt: 'desc' }
        });
      },
      this.CACHE_STRATEGIES.assessment
    );
  }

  /**
   * Batch-optimized learning resource queries
   */
  async getBatchedLearningResources(
    filters: Array<{ category?: string; skillLevel?: string; limit?: number }>
  ): Promise<any[]> {
    // For now, let's simplify this to avoid the batching complexity
    // and just execute the query directly
    const categoryFilters = Array.from(new Set(filters.map(f => f.category).filter(Boolean)));
    const skillLevelFilters = Array.from(new Set(filters.map(f => f.skillLevel).filter(Boolean)));
    const maxLimit = Math.max(...filters.map(f => f.limit || 10));

    const whereClause: any = {
      isActive: true,
      ...(categoryFilters.length > 0 && { category: { in: categoryFilters } }),
      ...(skillLevelFilters.length > 0 && { skillLevel: { in: skillLevelFilters } })
    };

    const resources = await prisma.learningResource.findMany({
      where: whereClause,
      select: {
        id: true,
        title: true,
        description: true,
        type: true,
        category: true,
        skillLevel: true,
        url: true,
        duration: true,
        isActive: true
      },
      orderBy: [
        { createdAt: 'desc' },
        { title: 'asc' }
      ],
      take: maxLimit * 2 // Get extra to allow filtering
    });

    // Return filtered results for each original filter
    return filters.map(filter => {
      return resources
        .filter(resource => {
          if (filter.category && resource.category !== filter.category) return false;
          if (filter.skillLevel && resource.skillLevel !== filter.skillLevel) return false;
          return true;
        })
        .slice(0, filter.limit || 10);
    });
  }

  /**
   * Optimized career path queries with static data caching
   */
  async getOptimizedCareerPaths(includeSteps: boolean = false): Promise<any[]> {
    const cacheKey = enhancedCacheKeyGenerator.generateKey(
      'career',
      'all_career_paths',
      { includeSteps }
    );

    return this.executeOptimizedQuery(
      cacheKey,
      async () => {
        const includeClause = includeSteps ? {
          steps: {
            select: {
              id: true,
              title: true,
              description: true,
              stepOrder: true,
              estimatedDuration: true
            },
            orderBy: { stepOrder: 'asc' }
          }
        } : {};

        return prisma.careerPath.findMany({
          where: { isActive: true },
          select: {
            id: true,
            name: true,
            slug: true,
            overview: true,
            pros: true,
            cons: true,
            actionableSteps: true,
            isActive: true,
            ...includeClause
          },
          orderBy: { createdAt: 'desc' }
        });
      },
      this.CACHE_STRATEGIES.careerPaths
    );
  }

  /**
   * Optimized user progress tracking with minimal data transfer
   */
  async getOptimizedUserProgress(userId: string, pathIds?: string[]): Promise<any[]> {
    const cacheKey = enhancedCacheKeyGenerator.generateKey(
      'user',
      'progress_tracking',
      { pathIds: pathIds?.sort() },
      userId
    );

    return this.executeOptimizedQuery(
      cacheKey,
      async () => {
        const whereClause: any = { userId };
        if (pathIds && pathIds.length > 0) {
          whereClause.learningPathId = { in: pathIds };
        }

        return prisma.userLearningPath.findMany({
          where: whereClause,
          select: {
            id: true,
            learningPathId: true,
            status: true,
            progressPercent: true,
            completedSteps: true,
            totalSteps: true,
            lastAccessedAt: true,
            startedAt: true,
            completedAt: true,
            learningPath: {
              select: {
                id: true,
                title: true,
                category: true,
                difficulty: true
              }
            }
          },
          orderBy: { lastAccessedAt: 'desc' }
        });
      },
      this.CACHE_STRATEGIES.userProgress
    );
  }

  /**
   * Execute optimized query with caching and metrics
   */
  private async executeOptimizedQuery<T>(
    cacheKey: string,
    queryFn: () => Promise<T>,
    optimization: QueryOptimization
  ): Promise<T> {
    const startTime = performance.now();

    try {
      // Check cache if enabled
      if (optimization.useCache) {
        const cached = await consolidatedCache.get<T>(cacheKey);
        if (cached !== null) {
          this.recordQueryMetric(cacheKey, performance.now() - startTime, true);
          return cached;
        }
      }

      // Execute query
      const result = await queryFn();
      const executionTime = performance.now() - startTime;

      // Cache result if enabled
      if (optimization.useCache) {
        await consolidatedCache.set(cacheKey, result, { ttl: optimization.cacheTTL * 1000, tags: ['query_cache'] });
        enhancedCacheKeyGenerator.updateKeyExpiration(cacheKey, optimization.cacheTTL * 1000);
      }

      this.recordQueryMetric(cacheKey, executionTime, false);
      return result;

    } catch (error) {
      console.error(`Optimized query failed for ${cacheKey}:`, error);
      throw error;
    }
  }

  /**
   * Execute batched query for improved performance (simplified version)
   */
  private async executeBatchedQuery<T, P>(
    batchKey: string,
    batchQueryFn: (params: P[]) => Promise<T[]>,
    params: P,
    optimization: QueryOptimization
  ): Promise<T> {
    // Simplified: just execute the query directly for now
    const results = await batchQueryFn([params]);
    return results[0];
  }

  /**
   * Execute batch of queries
   */
  private async executeBatch<T, P>(
    batchKey: string,
    batchQueryFn: (params: P[]) => Promise<T[]>,
    optimization: QueryOptimization
  ): Promise<void> {
    const queue = this.batchQueues.get(batchKey);
    if (!queue || queue.length === 0) return;

    // Clear timer and reset queue
    const timer = this.batchTimers.get(batchKey);
    if (timer) {
      clearTimeout(timer);
      this.batchTimers.delete(batchKey);
    }
    this.batchQueues.set(batchKey, []);

    const startTime = performance.now();

    try {
      // Extract all parameters
      const allParams = queue.map(q => q.query);
      
      // Execute batch query
      const results = await Promise.all(allParams.map(queryFn => queryFn()));

      // Resolve individual promises
      queue.forEach((batchedQuery, index) => {
        batchedQuery.resolve(results[index]);
      });

      this.recordQueryMetric(batchKey, performance.now() - startTime, false);

    } catch (error) {
      // Reject all promises
      queue.forEach(batchedQuery => {
        batchedQuery.reject(error);
      });
    }
  }

  /**
   * Record query performance metrics
   */
  private recordQueryMetric(queryKey: string, executionTime: number, fromCache: boolean): void {
    const existing = this.queryMetrics.get(queryKey) || { count: 0, totalTime: 0, avgTime: 0 };
    
    existing.count++;
    existing.totalTime += executionTime;
    existing.avgTime = existing.totalTime / existing.count;

    this.queryMetrics.set(queryKey, existing);

    // Log slow queries
    if (executionTime > 1000 && !fromCache) {
      console.warn(`Slow query detected: ${queryKey} took ${executionTime.toFixed(2)}ms`);
    }
  }

  /**
   * Get query performance metrics
   */
  getQueryMetrics(): Map<string, { count: number; totalTime: number; avgTime: number }> {
    return new Map(this.queryMetrics);
  }

  /**
   * Clear query metrics
   */
  clearMetrics(): void {
    this.queryMetrics.clear();
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.batchTimers.forEach((timer) => {
      clearTimeout(timer);
    });
    this.batchTimers.clear();
    this.batchQueues.clear();
  }
}

export const optimizedQueryService = OptimizedQueryService.getInstance();
