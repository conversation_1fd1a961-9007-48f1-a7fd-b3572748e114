/**
 * Advanced Request Deduplication Service for AI Service Calls
 * 
 * Implements sophisticated deduplication strategies including:
 * - Semantic similarity detection for AI prompts
 * - Cross-user deduplication for common requests
 * - Intelligent cache warming based on request patterns
 * - Request fingerprinting with fuzzy matching
 */

import { consolidatedCache } from './services/consolidated-cache-service';
import { AIServiceLogger } from './services/geminiService';
import crypto from 'crypto';

interface DeduplicationConfig {
  enableSemanticSimilarity: boolean;
  enableCrossUserDeduplication: boolean;
  enablePredictiveWarming: boolean;
  similarityThreshold: number; // 0-1, higher = more strict
  deduplicationWindow: number; // milliseconds
  maxPendingRequests: number;
  enableFuzzyMatching: boolean;
  warmingPatterns: string[];
}

interface RequestFingerprint {
  exactHash: string;
  semanticHash: string;
  normalizedParams: any;
  requestType: string;
  timestamp: number;
  userId?: string;
  priority: number;
}

interface PendingRequest {
  id: string;
  fingerprint: RequestFingerprint;
  resolve: (value: any) => void;
  reject: (error: any) => void;
  timestamp: number;
  userId?: string;
  timeout?: NodeJS.Timeout;
}

interface DeduplicationMetrics {
  totalRequests: number;
  exactDuplicates: number;
  semanticDuplicates: number;
  crossUserDuplicates: number;
  cacheWarmed: number;
  averageResponseTime: number;
  deduplicationSavings: number; // estimated time saved in ms
}

export class AdvancedRequestDeduplicationService {
  private config: DeduplicationConfig;
  private pendingRequests: Map<string, PendingRequest[]>;
  private requestHistory: Map<string, { timestamp: number; result: any }>;
  private semanticCache: Map<string, string[]>; // semantic hash -> exact hashes
  private metrics: DeduplicationMetrics;
  private warmingQueue: Set<string>;

  constructor(config?: Partial<DeduplicationConfig>) {
    this.config = {
      enableSemanticSimilarity: true,
      enableCrossUserDeduplication: true,
      enablePredictiveWarming: true,
      similarityThreshold: 0.85,
      deduplicationWindow: 30000, // 30 seconds
      maxPendingRequests: 100,
      enableFuzzyMatching: true,
      warmingPatterns: [
        'skills-analysis:*',
        'career-recommendations:*',
        'interview-questions:*'
      ],
      ...config
    };

    this.pendingRequests = new Map();
    this.requestHistory = new Map();
    this.semanticCache = new Map();
    this.warmingQueue = new Set();
    
    this.metrics = {
      totalRequests: 0,
      exactDuplicates: 0,
      semanticDuplicates: 0,
      crossUserDuplicates: 0,
      cacheWarmed: 0,
      averageResponseTime: 0,
      deduplicationSavings: 0
    };

    this.startBackgroundProcesses();
    
    AIServiceLogger.info('Advanced Request Deduplication Service initialized', {
      config: this.config
    });
  }

  /**
   * Main deduplication method for AI service calls
   */
  async deduplicateRequest<T>(
    requestKey: string,
    requestFunction: () => Promise<T>,
    options: {
      userId?: string;
      priority?: number;
      timeout?: number;
      enableSemanticMatch?: boolean;
      enableCrossUserMatch?: boolean;
    } = {}
  ): Promise<T> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      // Generate request fingerprint
      const fingerprint = this.generateRequestFingerprint(requestKey, options);

      // Check for exact duplicate in history
      const exactKey = fingerprint.exactHash;
      const exactHistory = this.requestHistory.get(exactKey);
      if (exactHistory && (Date.now() - exactHistory.timestamp) < this.config.deduplicationWindow) {
        this.metrics.exactDuplicates++;
        this.updateMetrics(startTime);
        return exactHistory.result;
      }

      // Check for semantic similarity if enabled
      if (this.config.enableSemanticSimilarity && options.enableSemanticMatch !== false) {
        const semanticKey = `semantic:${fingerprint.semanticHash}`;
        const semanticHistory = this.requestHistory.get(semanticKey);
        if (semanticHistory && (Date.now() - semanticHistory.timestamp) < this.config.deduplicationWindow) {
          this.metrics.semanticDuplicates++;
          this.updateMetrics(startTime);
          return semanticHistory.result;
        }
      }

      // Check for cross-user deduplication if enabled
      if (this.config.enableCrossUserDeduplication && options.enableCrossUserMatch !== false) {
        if (this.isSafeForCrossUserSharing(fingerprint.requestType)) {
          const crossUserKey = `cross_user:${fingerprint.semanticHash}`;
          const crossUserHistory = this.requestHistory.get(crossUserKey);
          if (crossUserHistory && (Date.now() - crossUserHistory.timestamp) < this.config.deduplicationWindow) {
            this.metrics.crossUserDuplicates++;
            this.updateMetrics(startTime);
            return crossUserHistory.result;
          }
        }
      }

      // Execute request with deduplication tracking
      return await this.executeWithDeduplication<T>(fingerprint, requestFunction, options);

    } catch (error) {
      AIServiceLogger.error('Request deduplication failed', {
        requestKey,
        error: error instanceof Error ? error.message : String(error)
      });
      // Fallback to direct execution
      return await requestFunction();
    }
  }

  /**
   * Generate comprehensive request fingerprint
   */
  private generateRequestFingerprint(
    requestKey: string, 
    options: any
  ): RequestFingerprint {
    // Extract request type and parameters
    const [requestType, ...params] = requestKey.split(':');
    
    // Normalize parameters for consistent hashing
    const normalizedParams = this.normalizeParameters(params.join(':'));
    
    // Generate exact hash
    const exactHash = crypto
      .createHash('sha256')
      .update(JSON.stringify({ requestType, normalizedParams, userId: options.userId }))
      .digest('hex');

    // Generate semantic hash (without user-specific data)
    const semanticHash = crypto
      .createHash('sha256')
      .update(JSON.stringify({ requestType, normalizedParams }))
      .digest('hex');

    return {
      exactHash,
      semanticHash,
      normalizedParams,
      requestType,
      timestamp: Date.now(),
      userId: options.userId,
      priority: options.priority || 1
    };
  }

  /**
   * Normalize parameters to improve deduplication accuracy
   */
  private normalizeParameters(params: string): any {
    try {
      // Handle common parameter variations
      let normalized = params
        .toLowerCase()
        .replace(/\s+/g, ' ')
        .replace(/\s*,\s*/g, ',')  // Normalize spaces around commas
        .replace(/\s*:\s*/g, ':')  // Normalize spaces around colons
        .trim();

      // Normalize common career path variations
      normalized = normalized
        .replace(/software engineer/g, 'software_engineer')
        .replace(/data scientist/g, 'data_scientist')
        .replace(/product manager/g, 'product_manager')
        .replace(/full stack/g, 'fullstack')
        .replace(/front end/g, 'frontend')
        .replace(/back end/g, 'backend');

      // Normalize experience levels
      normalized = normalized
        .replace(/entry.?level/g, 'entry_level')
        .replace(/mid.?level/g, 'mid_level')
        .replace(/senior.?level/g, 'senior_level');

      return normalized;
    } catch (error) {
      return params;
    }
  }

  /**
   * Check for exact duplicate requests
   */
  private async checkExactDuplicate<T>(fingerprint: RequestFingerprint): Promise<T | null> {
    // Check recent history first
    const recent = this.requestHistory.get(fingerprint.exactHash);
    if (recent && (Date.now() - recent.timestamp) < this.config.deduplicationWindow) {
      return recent.result;
    }

    // Check pending requests
    const pending = this.pendingRequests.get(fingerprint.exactHash);
    if (pending && pending.length > 0) {
      // Wait for existing request to complete
      return new Promise((resolve, reject) => {
        const request: PendingRequest = {
          id: this.generateRequestId(),
          fingerprint,
          resolve,
          reject,
          timestamp: Date.now(),
          userId: fingerprint.userId
        };

        pending.push(request);

        // Set timeout for waiting
        request.timeout = setTimeout(() => {
          const index = pending.indexOf(request);
          if (index > -1) {
            pending.splice(index, 1);
            reject(new Error('Deduplication timeout'));
          }
        }, this.config.deduplicationWindow);
      });
    }

    return null;
  }

  /**
   * Check for semantically similar requests
   */
  private async checkSemanticDuplicate<T>(fingerprint: RequestFingerprint): Promise<T | null> {
    if (!this.config.enableSemanticSimilarity) return null;

    // Look for similar semantic hashes
    const similarHashes = this.semanticCache.get(fingerprint.semanticHash);
    if (similarHashes && similarHashes.length > 0) {
      // Check if any similar request has recent results
      for (const hash of similarHashes) {
        const recent = this.requestHistory.get(hash);
        if (recent && (Date.now() - recent.timestamp) < this.config.deduplicationWindow) {
          // For semantic similarity, we consider normalized parameters as similar enough
          // if they have the same semantic hash
          return recent.result;
        }
      }
    }

    // Also check if there's a recent result with the same semantic hash directly
    const semanticKey = `semantic:${fingerprint.semanticHash}`;
    const semanticResult = this.requestHistory.get(semanticKey);
    if (semanticResult && (Date.now() - semanticResult.timestamp) < this.config.deduplicationWindow) {
      return semanticResult.result;
    }

    return null;
  }

  /**
   * Check for cross-user deduplication opportunities
   */
  private async checkCrossUserDuplicate<T>(fingerprint: RequestFingerprint): Promise<T | null> {
    if (!this.config.enableCrossUserDeduplication) return null;

    // For cross-user deduplication, we use semantic hash without user ID
    const crossUserKey = `cross_user:${fingerprint.semanticHash}`;
    const recent = this.requestHistory.get(crossUserKey);
    
    if (recent && (Date.now() - recent.timestamp) < this.config.deduplicationWindow) {
      // Only return if the request is for common, non-sensitive data
      if (this.isSafeForCrossUserSharing(fingerprint.requestType)) {
        return recent.result;
      }
    }

    return null;
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private calculateSimilarity(params1: any, params2: any): number {
    // Simplified similarity calculation
    // In production, this could use more sophisticated NLP techniques
    const str1 = JSON.stringify(params1);
    const str2 = JSON.stringify(params2);
    
    if (str1 === str2) return 1.0;
    
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  private isSafeForCrossUserSharing(requestType: string): boolean {
    const safeTypes = [
      'career-recommendations',
      'interview-questions',
      'market-data',
      'skill-definitions'
    ];
    return safeTypes.includes(requestType);
  }

  private updateMetrics(startTime: number): void {
    const responseTime = Date.now() - startTime;

    // Update average response time for all requests (including duplicates)
    if (this.metrics.totalRequests === 1) {
      this.metrics.averageResponseTime = responseTime;
    } else {
      this.metrics.averageResponseTime =
        (this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + responseTime) /
        this.metrics.totalRequests;
    }

    // Calculate deduplication savings (estimated time saved by not making duplicate requests)
    const estimatedAICallTime = 2000; // Assume 2 seconds average for AI calls
    this.metrics.deduplicationSavings += estimatedAICallTime;
  }

  private startBackgroundProcesses(): void {
    // Cleanup expired entries every 5 minutes
    setInterval(() => {
      this.cleanupExpiredEntries();
    }, 5 * 60 * 1000);

    // Predictive cache warming every 10 minutes
    if (this.config.enablePredictiveWarming) {
      setInterval(() => {
        this.performPredictiveWarming();
      }, 10 * 60 * 1000);
    }
  }

  private cleanupExpiredEntries(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    Array.from(this.requestHistory.entries()).forEach(([key, entry]) => {
      if (now - entry.timestamp > this.config.deduplicationWindow * 2) {
        expiredKeys.push(key);
      }
    });

    expiredKeys.forEach(key => this.requestHistory.delete(key));
    
    AIServiceLogger.debug('Cleaned up expired deduplication entries', { 
      count: expiredKeys.length 
    });
  }

  private async performPredictiveWarming(): Promise<void> {
    // Implementation for predictive cache warming based on patterns
    // This would analyze request patterns and pre-warm likely requests
    AIServiceLogger.debug('Performing predictive cache warming');
  }

  async executeWithDeduplication<T>(
    fingerprint: RequestFingerprint,
    requestFunction: () => Promise<T>,
    options: any
  ): Promise<T> {
    const startTime = Date.now();

    try {
      const result = await requestFunction();

      // Store result in history for exact deduplication
      this.requestHistory.set(fingerprint.exactHash, {
        timestamp: Date.now(),
        result
      });

      // Store semantic result for semantic deduplication
      if (this.config.enableSemanticSimilarity) {
        const semanticKey = `semantic:${fingerprint.semanticHash}`;
        this.requestHistory.set(semanticKey, {
          timestamp: Date.now(),
          result
        });
      }

      // Store cross-user result if applicable
      if (this.config.enableCrossUserDeduplication && this.isSafeForCrossUserSharing(fingerprint.requestType)) {
        const crossUserKey = `cross_user:${fingerprint.semanticHash}`;
        this.requestHistory.set(crossUserKey, {
          timestamp: Date.now(),
          result
        });
      }

      // Update metrics
      this.updateMetrics(startTime);

      return result;

    } catch (error) {
      throw error;
    }
  }

  getMetrics(): DeduplicationMetrics {
    return { ...this.metrics };
  }
}

// Export singleton instance
export const advancedRequestDeduplication = new AdvancedRequestDeduplicationService();
