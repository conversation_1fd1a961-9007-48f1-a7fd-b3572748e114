/**
 * AI Service Interfaces
 * 
 * Core interfaces for AI services to enable dependency injection
 * and reduce coupling between AI components.
 */

// Core AI Response Types

export interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
  cached?: boolean;
  metadata?: {
    responseTime: number;
    model: string;
    tokensUsed?: number;
    cacheHit?: boolean;
    retryCount?: number;
  };
}

export interface AIOptions {
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
  retries?: number;
  cacheKey?: string;
  cacheTTL?: number;
  userId?: string;
  priority?: number;
}

export interface HealthStatus {
  ai: boolean;
  cache: {
    redis: boolean;
    memory: boolean;
  };
  responseTime?: number;
  lastCheck?: Date;
}

// Skill Analysis Types

export interface SkillGapParams {
  currentSkills: string[];
  targetCareerPath: string;
  experienceLevel: string;
  userId?: string;
}

export interface CareerParams {
  assessmentData: any;
  skills: string[];
  preferences: any;
  userId?: string;
}

export interface InterviewParams {
  careerPath: string;
  experienceLevel: string;
  questionType: string;
  count?: number;
  userId?: string;
}

export interface ResumeAnalysisParams {
  resumeText: string;
  targetRole?: string;
  userId?: string;
}

// Cache Service Types

export interface CacheOptions {
  ttl?: number;
  tags?: string[];
  priority?: number;
  compress?: boolean;
}

export interface CacheMetrics {
  hits: number;
  misses: number;
  hitRate: number;
  totalRequests: number;
  averageResponseTime: number;
  memoryUsage: number;
}

// Logger Types

export interface LogContext {
  userId?: string;
  requestId?: string;
  operation?: string;
  duration?: number;
  [key: string]: any;
}

// Core AI Service Interfaces

export interface IAIService {
  generateContent(prompt: string, options?: AIOptions): Promise<AIResponse>;
  analyzeSkillsGap(params: SkillGapParams): Promise<AIResponse>;
  generateCareerRecommendations(params: CareerParams): Promise<AIResponse>;
  generateInterviewQuestions(params: InterviewParams): Promise<AIResponse>;
  analyzeResume(params: ResumeAnalysisParams): Promise<AIResponse>;
  healthCheck(): Promise<HealthStatus>;
  initialize(): Promise<void>;
}

export interface ICacheService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, options?: CacheOptions): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  invalidateByTags(tags: string[]): Promise<number>;
  getMetrics(): CacheMetrics;
  deduplicate<T>(key: string, requestFunction: () => Promise<T>, options?: CacheOptions): Promise<T>;
}

export interface ILogger {
  info(message: string, context?: LogContext): void;
  warn(message: string, context?: LogContext): void;
  error(message: string, error?: any, context?: LogContext): void;
  debug(message: string, context?: LogContext): void;
}

export interface IInputValidator {
  validatePrompt(prompt: string): ValidationResult;
  validateSkillsData(skills: any): ValidationResult;
  validateAssessmentData(data: any): ValidationResult;
  sanitizeInput(input: any): any;
  securityScan(input: string): SecurityScanResult;
}

export interface IResponseParser {
  parseJSON<T>(response: string): T | null;
  validateResponse(response: any, schema?: any): boolean;
  extractContent(response: any): string;
  handleParsingError(error: Error, response: string): any;
}

// Configuration Interfaces

export interface AIConfig {
  provider: 'gemini' | 'openai' | 'anthropic';
  apiKey: string;
  model: string;
  timeout: number;
  retries: number;
  rateLimitPerMinute: number;
  enableCaching: boolean;
  cacheTTL: number;
  enableMetrics: boolean;
  enableFallbacks: boolean;
}

export interface CacheConfig {
  provider: 'redis' | 'memory' | 'hybrid';
  redisUrl?: string;
  maxMemorySize: number;
  defaultTTL: number;
  enableCompression: boolean;
  compressionThreshold: number;
  enableMetrics: boolean;
}

export interface ValidationConfig {
  maxPromptLength: number;
  maxResponseLength: number;
  enableSecurityScan: boolean;
  blockedPatterns: string[];
  allowedContentTypes: string[];
}

// Service Factory Interfaces

export interface IAIServiceFactory {
  createAIService(provider: string, config: AIConfig): IAIService;
  createCacheService(config: CacheConfig): ICacheService;
  createValidator(config: ValidationConfig): IInputValidator;
  createResponseParser(): IResponseParser;
  createLogger(): ILogger;
}

// Monitoring and Performance Interfaces

export interface IPerformanceMonitor {
  startTimer(operation: string): string;
  endTimer(timerId: string): number;
  recordMetric(name: string, value: number, tags?: Record<string, string>): void;
  getMetrics(): Record<string, any>;
  reset(): void;
}

export interface IRateLimiter {
  checkLimit(userId: string, operation: string): Promise<boolean>;
  incrementUsage(userId: string, operation: string): Promise<void>;
  getRemainingRequests(userId: string, operation: string): Promise<number>;
  resetLimit(userId: string, operation: string): Promise<void>;
}

// Error Types

export class AIServiceError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly retryable: boolean = false,
    public readonly context?: any
  ) {
    super(message);
    this.name = 'AIServiceError';
  }
}

export class ValidationError extends AIServiceError {
  constructor(message: string, public readonly field?: string) {
    super(message, 'VALIDATION_ERROR', false);
    this.name = 'ValidationError';
  }
}

export class CacheError extends AIServiceError {
  constructor(message: string, retryable: boolean = true) {
    super(message, 'CACHE_ERROR', retryable);
    this.name = 'CacheError';
  }
}

export class RateLimitError extends AIServiceError {
  constructor(message: string, public readonly retryAfter: number) {
    super(message, 'RATE_LIMIT_ERROR', true);
    this.name = 'RateLimitError';
  }
}

export class ModelError extends AIServiceError {
  constructor(message: string, public readonly modelResponse?: any) {
    super(message, 'MODEL_ERROR', true);
    this.name = 'ModelError';
  }
}

// Type Guards

export function isAIResponse(obj: any): obj is AIResponse {
  return obj && typeof obj.success === 'boolean';
}

export function isHealthStatus(obj: any): obj is HealthStatus {
  return obj && 
    typeof obj.ai === 'boolean' && 
    obj.cache && 
    typeof obj.cache.redis === 'boolean' && 
    typeof obj.cache.memory === 'boolean';
}

export function isCacheMetrics(obj: any): obj is CacheMetrics {
  return obj && 
    typeof obj.hits === 'number' && 
    typeof obj.misses === 'number' && 
    typeof obj.hitRate === 'number';
}

// Utility Types

export type AIServiceProvider = 'gemini' | 'openai' | 'anthropic';
export type CacheProvider = 'redis' | 'memory' | 'hybrid';
export type AIOperation = 'skills-analysis' | 'career-recommendations' | 'interview-questions' | 'resume-analysis';
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// Constants

export const AI_CONSTANTS = {
  DEFAULT_TIMEOUT: 30000,
  DEFAULT_RETRIES: 3,
  DEFAULT_CACHE_TTL: 3600000, // 1 hour
  MAX_PROMPT_LENGTH: 50000,
  MAX_RESPONSE_LENGTH: 100000,
  RATE_LIMIT_WINDOW: 60000, // 1 minute
  DEFAULT_RATE_LIMIT: 60
} as const;

export const CACHE_KEYS = {
  SKILLS_ANALYSIS: 'skills-analysis',
  CAREER_RECOMMENDATIONS: 'career-recommendations',
  INTERVIEW_QUESTIONS: 'interview-questions',
  RESUME_ANALYSIS: 'resume-analysis'
} as const;

// Helper Functions

export function createAIOptions(overrides: Partial<AIOptions> = {}): AIOptions {
  return {
    temperature: 0.7,
    maxTokens: 4000,
    timeout: AI_CONSTANTS.DEFAULT_TIMEOUT,
    retries: AI_CONSTANTS.DEFAULT_RETRIES,
    cacheTTL: AI_CONSTANTS.DEFAULT_CACHE_TTL,
    ...overrides
  };
}

export function createCacheOptions(overrides: Partial<CacheOptions> = {}): CacheOptions {
  return {
    ttl: AI_CONSTANTS.DEFAULT_CACHE_TTL,
    tags: [],
    priority: 1,
    compress: false,
    ...overrides
  };
}
