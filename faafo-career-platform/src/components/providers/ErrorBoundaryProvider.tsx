'use client';

import React, { ReactNode } from 'react';
import { UnifiedErrorBoundary, NetworkAwareErrorBoundary } from '@/components/unified-error-boundary';
import { SecurityErrorBoundary } from '@/components/security/SecurityErrorBoundary';

interface ErrorBoundaryProviderProps {
  children: ReactNode;
}

export function ErrorBoundaryProvider({ children }: ErrorBoundaryProviderProps) {
  return (
    <UnifiedErrorBoundary
      context="root"
      maxRetries={1}
      autoRetry={false}
      enableRecovery={true}
      enableReporting={true}
      showErrorDetails={false}
      onError={(error, errorInfo) => {
        console.error('Root Error Boundary:', { error, errorInfo });

        // Report to error tracking service
        if (process.env.NODE_ENV === 'production') {
          // Send to Sentry or other error tracking service
          if (typeof window !== 'undefined' && (window as any).Sentry) {
            (window as any).Sentry.captureException(error, {
              contexts: {
                react: errorInfo
              },
              tags: {
                errorBoundary: 'root',
                level: 'critical'
              }
            });
          }
        }
      }}
    >
      <SecurityErrorBoundary context="general">
        <NetworkAwareErrorBoundary>
          {children}
        </NetworkAwareErrorBoundary>
      </SecurityErrorBoundary>
    </UnifiedErrorBoundary>
  );
}

// Specialized providers for different sections of the app
export function AuthSectionProvider({ children }: { children: ReactNode }) {
  return (
    <UnifiedErrorBoundary
      context="auth"
      maxRetries={2}
      autoRetry={false}
      enableRecovery={true}
      onError={(error, errorInfo) => {
        console.error('Auth Section Error:', { error, errorInfo });

        // Clear any potentially corrupted auth state
        if (typeof window !== 'undefined') {
          // Clear session storage
          sessionStorage.removeItem('next-auth.session-token');
          sessionStorage.removeItem('next-auth.csrf-token');

          // Clear any auth-related localStorage
          Object.keys(localStorage).forEach(key => {
            if (key.includes('auth') || key.includes('session')) {
              localStorage.removeItem(key);
            }
          });
        }
      }}
    >
      {children}
    </UnifiedErrorBoundary>
  );
}

export function FormSectionProvider({ children }: { children: ReactNode }) {
  return (
    <UnifiedErrorBoundary
      context="forms"
      maxRetries={2}
      autoRetry={true}
      enableRecovery={true}
      onError={(error, errorInfo) => {
        console.error('Form Section Error:', { error, errorInfo });

        // Clear any potentially corrupted CSRF tokens
        if (typeof window !== 'undefined') {
          // Clear CSRF-related storage
          Object.keys(sessionStorage).forEach(key => {
            if (key.includes('csrf') || key.includes('token')) {
              sessionStorage.removeItem(key);
            }
          });
        }
      }}
    >
      {children}
    </UnifiedErrorBoundary>
  );
}

export function APISectionProvider({ children }: { children: ReactNode }) {
  return (
    <NetworkAwareErrorBoundary>
      <UnifiedErrorBoundary
        context="api"
        maxRetries={3}
        autoRetry={true}
        enableRecovery={true}
        onError={(error, errorInfo) => {
          console.error('API Section Error:', { error, errorInfo });

          // Log API error details for debugging
          const apiErrorDetails = {
            error: error.message,
            stack: error.stack,
            componentStack: errorInfo.componentStack,
            timestamp: new Date().toISOString(),
            url: typeof window !== 'undefined' ? window.location.href : 'server'
          };

          console.error('API Error Details:', apiErrorDetails);
        }}
      >
        {children}
      </UnifiedErrorBoundary>
    </NetworkAwareErrorBoundary>
  );
}

export default ErrorBoundaryProvider;
